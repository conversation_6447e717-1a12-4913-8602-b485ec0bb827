import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import Page from '@/components/Page'
import style from './index.module.scss'
import HeadTitle from './components/HeadTitle'
import JobType from './components/JobType'
import ResumeFooter from '@/pages/resume/components/ResumePublish/components/ResumeFooter'
import { getBtmHeight, getShowTemplate } from '../utils'
import General from './components/General'
import { geTemplate, handleTemData, init, temMustHandle } from './utils'
import { savePublishData } from '@/core/utils/publish'
import SalaryPicker from './components/SalaryPicker'
import CustomNavbar from '@/components/CustomNavbar'
import ExpectPop from './components/ExpectPop'
import { addOrUpdateSub, delSub, formatVal, publishOrUpdateSub } from './publishUtils'

export default function Index() {
  const { publishFlow, params, template: dtemplate, publishRuleSwitch, isBack } = $.router.data
  const [codeObj] = useState([
    // 期望职位
    'F_WorkType',
    // 求职类型
    'F_WorkNature',
    // 日薪
    'F_SalaryDay',
    // 月薪
    'F_SalaryMonth',
    // 职位偏好
    'F_Preference',
  ])
  const [btmHeight, setBtmHeight] = useState(0)
  const [salaryPickerVis, setSalaryPickerVis] = useState(false)
  const [expectPopVis, setExpectPopVis] = useState(false)
  const [query, setQuery] = useState({
    // 类型 1.添加 2.修改
    type: 1,
    /** 工种id,多个用,分割 */
    occIds: '',
    /** 来源页  add:简历发布  edit:编辑简历 */
    origin: 'add',
    // 子名片ID
    resumeSubUuid: '',
    /** 子名片总数 */
    numMax: 1,
    /** 全职可选数量 */
    numFull: 1,
    /** 兼职可选数量 */
    numPart: 1,
  })
  // 求职类型 1.全职 2.兼职
  const [data, setData] = useState<any>({
    // 快速发布页职位和城市数据
    occAreaReq: {} as any,
    // 模板
    template: {} as any,
    // 职位偏好模板
    perTemplate: {} as any,
    // 求职类型 1.全职 2.兼职
    positionType: 1,
    // 职位偏好选中的值
    preferenceList: [],
    // 薪资选中的值
    salaryObj: {} as any,
    // 职位名称显示文案
    occNames: '',
    // 职位偏好显示文案
    preference: '',
    // 按钮显示的文案
    subBtnTxt: '保存',
    // 薪资要求显示文案
    salary: '',
    // 必填项
    mustObj: {
      // 期望职位
      F_WorkType: { status: true, must: true },
      // 求职类型
      F_WorkNature: { status: true },
      // 日薪
      F_SalaryDay: { status: true },
      // 月薪
      F_SalaryMonth: { status: true },
      // 职位偏好
      F_Preference: { status: false },
    } as any,
    // 职业偏好里的必填项
    preMustObj: {} as any,
    // 用户手动选择的工种
    uSelectOcc: [],
    // 切换全职或者兼职的缓存数据
    oldOriData: {},
    // 弹框的确定按钮loading状态
    ePopBtnLoading: false,
    // 手动选择过的薪资 key为positionType的值
    userSelectedSalary: {},
    // 手动选择过的工种的模板已有的数据 key为occid_positionType的值
    subObj: {},
    // 初始进入的所有值
    oSub: {
      positionType: 1,
    },
    isShow: false,
  })

  useEffect(() => {
    setTimeout(() => {
      getBtmHeight(setBtmHeight)
    }, 100)
  }, [])

  useLoad(async (options) => {
    $.showLoading('加载中...')
    const { positionType, ...other } = options || {}
    const { occAreaReq } = params || {}
    const nQuery = { ...query, ...other }
    setQuery(nQuery)
    const nData = { ...data, occAreaReq, template: dtemplate, positionType: positionType || 1 }
    setData(nData)
    await init(false, [], { codeObj, publishFlow, query: nQuery, ...nData }, { setData, onPerFectChange, setQuery })
    $.hideLoading()
  })

  useEffect(() => {
    const { occAreaReq, preferenceList } = data || {}
    const { occupations } = occAreaReq || {}
    let occNames = ''
    if ($.isArrayVal(occupations)) {
      occNames = occupations.map((occ) => occ.name).filter(name => name).join('、')
    }
    setData((prev) => ({ ...prev, occNames }))
    if ($.isArrayVal(preferenceList)) {
      onPerFectChange(preferenceList)
    }
  }, [data.occAreaReq])

  const onPerFectChange = (valArr) => {
    const { origin } = query || {}
    const sData: any = {}
    if ($.isArrayVal(valArr)) {
      sData.preferenceList = valArr
      sData.preference = valArr.map(val => {
        if (['INPUT_STRING', 'INPUT_NUMBER'].includes(val.controlTypeCode)) {
          return val.controlValues
        }
        if (['INPUT_WITH_UNIT'].includes(val.controlTypeCode)) {
          if (val.controlNames) {
            return `${val.label}${val.controlNames}`
          }
          return `${val.controlName}${val.controlValues}${val.unit || ''}`
        }
        return val.controlNames
      }).filter(name => name).join('、')
    } else {
      sData.preference = ''
      sData.preferenceList = []
    }
    if (origin == 'add') {
      savePublishData('preferenceList', valArr)
    }
    setData(prev => ({ ...prev, ...sData }))
  }

  const onSwitchChange = async (e) => {
    const { value: val } = e.detail
    const { oldOriData, template,
      salaryObj, salary, preferenceList,
      preference, positionType, subObj, userSelectedSalary } = data
    if (val == positionType) {
      return
    }
    const { origin, type } = query || {}
    if (type == 1 && origin == 'add') {
      savePublishData('positionType', val)
      savePublishData('salaryObj', {})
      savePublishData('preferenceList', [])
    }

    const temMust = temMustHandle(template, codeObj, origin)
    const nOldOriData = {
      ...oldOriData,
      [positionType]: {
        salaryObj,
        salary,
        preferenceList,
        preference,
      },
    }
    const { occId } = template || {}
    // 缓存在数据库的数据
    const oodata = handleTemData(template, userSelectedSalary, subObj[`${occId}_${val}`] || {})
    const { controlList } = await getShowTemplate({ positionType: val }, template, preferenceList)

    // 职位偏好的职位类型配置为all，相同工种切换求职类型，职位偏好不清空
    let preListTypeOfAll = preferenceList.filter(item => {
      const { controlCode } = item || {}
      return controlList.find((cl) => {
        const { code, cObj } = cl || {}
        // eslint-disable-next-line sonarjs/no-nested-functions
        const coArr = Object.values(cObj || {}).flat().map(coCode => `${coCode}`)
        return code == controlCode || coArr.includes(`${controlCode}`)
      })
    })
    const hc: any = oldOriData[val] || {}
    if ($.isArrayVal(preferenceList)) {
      let { preferenceList: pfList = [] } = hc || {}
      const ctCodes = pfList.map(item => item.controlCode)
      preListTypeOfAll = preListTypeOfAll.filter(item => !ctCodes.includes(item.controlCode))
      pfList = pfList.concat(preListTypeOfAll)
      hc.preferenceList = pfList
    }
    const sData = {
      positionType: val,
      salaryObj: {},
      salary: '',
      preferenceList: preListTypeOfAll,
      preference: '',
      ...temMust,
      ...(oodata || {}),
      ...(hc || {}),
      oldOriData: nOldOriData,
    }
    setData((prev) => ({ ...prev, ...sData }))
    const { preferenceList: pfList = [], salaryObj: oso } = sData || {}
    if ($.isArrayVal(pfList)) {
      onPerFectChange(pfList)
    }
    if (type == 1 && origin == 'add') {
      savePublishData('positionType', val)
      savePublishData('salaryObj', oso || {})
      savePublishData('preferenceList', pfList)
    }
  }

  const onSalaryClick = () => {
    setSalaryPickerVis(true)
  }

  const onSalarySubmit = (e) => {
    const { template, positionType, userSelectedSalary } = data
    const { origin } = query || {}
    const { templateInfo } = template || {}
    const { controlInfoList = [], code } = templateInfo || {}
    let control: any = {}
    if ($.isArrayVal(controlInfoList)) {
      control = controlInfoList.find(ctr => ctr.controlCode == (positionType == 1 ? 'F_SalaryMonth' : 'F_SalaryDay'))
    }
    const { controlCode, controlTypeCode } = control || {}
    const { subStr, valFormat } = e
    const salaryObj = {
      templateCode: code,
      controlCode,
      controlTypeCode,
      controlValues: subStr,
      valFormat,
    }
    if (origin == 'add') {
      savePublishData('salaryObj', salaryObj)
    }
    const sData = {
      salary: valFormat,
      salaryPickerVis: false,
      salaryObj,
      userSelectedSalary: { ...userSelectedSalary, [positionType]: { salary: valFormat, salaryObj } },
    }
    setSalaryPickerVis(false)
    setData((prev) => ({ ...prev, ...sData }))
  }

  const onPreferenceClick = async () => {
    const { template, occAreaReq, preferenceList, positionType, oSub } = data
    let { perTemplate } = data
    const { occId, positionType: oPositionType } = oSub || {}

    const { origin, resumeSubUuid } = query || {}
    const { occupations } = occAreaReq || {}

    let { occId: exOccId } = template || {}
    let occ:any = {}
    if (occupations && occupations.length) {
      occ = { ...occupations[0] }
      exOccId = occ.occupation
    }

    const { occId: perOccId } = perTemplate || {}
    if ($.isEmptyObject(perTemplate) || exOccId != perOccId) {
      $.showLoading('请求中...')
      perTemplate = await geTemplate([exOccId], ['RESUME_EDIT_PREFERENCE_DISPLAY'])
      $.hideLoading()
      setData((prev) => ({ ...prev, perTemplate }))
    }
    $.router.push(
      '/subpackage/resume-complete/perfect-job-v4/index',
      { occIds: exOccId, origin, positionType, oOccIds: occId, resumeSubUuid, oPositionType },
      { template: $.deepClone(perTemplate), preferenceList, occupation: occ },
      (list) => {
        onPerFectChange(list)
      },
    )
  }

  const onDelete = () => {
    $.confirm({
      content: '删除这条求职期望吗？',
    }).then(() => {
      delSub(query)
    })
  }
  const onWorkTypeClick = () => {
    const { positionType, uSelectOcc } = data
    const { numFull, numPart, type, origin } = query || {}
    let occNum = positionType == 1 ? numFull : numPart
    if (type == 2 && occNum > 1) {
      occNum = 1
    }

    if (origin == 'edit' && occNum > 0) {
      $.openClassify({
        isSelectToBack: true,
        value: occNum == 1 ? [] : (uSelectOcc || []).map(socc => ({ ...socc, id: socc.occupation })),
        maxSelectNum: occNum,
        title: `选择期望职位${occNum == 1 ? '' : '(可多选)'}`,
        maxSelectToastTxt: positionType == 1 ? '全职' : '兼职',
        source_id: 3,
      }, (data) => {
        onChangeValue(data)
      })
    }
  }
  const onChangeValue = async (occArrr) => {
    const { occAreaReq, userSelectedSalary, positionType } = data
    const occIdArr: Array<any> = []
    const occupations = occArrr.map((occ) => {
      occIdArr.push(occ.id)
      return {
        occupation: Number(occ.id),
        industries: occ.industries,
        mode: occ.mode,
        name: occ.name,
      }
    })

    const sData: any = {
      uSelectOcc: occupations,
      occAreaReq: { ...(occAreaReq || {}), occupations },
      salaryObj: {},
      salary: '',
      preferenceList: [],
      preference: '',
      oldOriData: {},
    }

    const oSalaryObj = userSelectedSalary[positionType]
    if ($.isObjVal(oSalaryObj)) {
      sData.salaryObj = oSalaryObj.salaryObj
      sData.salary = oSalaryObj.salary
    }
    if ($.isArrayVal(occIdArr)) {
      await init(true, occIdArr, { ...data, codeObj, publishFlow, query, ...sData, saveParams: sData }, { setData, onPerFectChange })
    }
  }
  const onConfirm = async () => {
    const { occAreaReq } = data
    const { origin } = query || {}
    const { occupations } = occAreaReq || {}
    if (!$.isArrayVal(occupations)) {
      $.msg('请选择期望职位')
      return
    }
    let visible = false
    if ($.isArrayVal(occupations) && occupations.length > 1) {
      if (formatVal(data)) {
        return
      }
      visible = true
    }
    setExpectPopVis(visible)
    if (visible) {
      return
    }
    if (origin == 'add') {
      await publishOrUpdateSub({ ...data, publishFlow, isBack, params })
    } else if (origin == 'edit') {
      await addOrUpdateSub({ ...data, publishFlow }, query)
    }
  }

  const onExpectConfirm = async () => {
    const { origin } = query || {}
    if (origin == 'add') {
      await publishOrUpdateSub({ ...data, publishFlow, isBack, params })
    } else if (origin == 'edit') {
      await addOrUpdateSub({ ...data, publishFlow }, query)
    }
    setExpectPopVis(false)
  }

  return (
    <Page backgroundColor='#fff'>
      <V className={style.page}>
        <CustomNavbar title=' ' />
        <V className={style.content}>
          <HeadTitle type={query.type} />
          <JobType mustObj={data.mustObj} query={query} positionType={data.positionType} onSwitchChange={onSwitchChange} />
          {/* 期望职位 */}
          <General type='F_WorkType' mustObj={data.mustObj} isNoWarp value={data.occNames} onClick={onWorkTypeClick} isClsChange={query.origin == 'edit'} label="期望职位" placeholder="请选择期望职位" />
          {/* 薪资要求 */}
          <General type={data.positionType == 1 ? 'F_SalaryMonth' : 'F_SalaryDay'} mustObj={data.mustObj} value={data.salary} onClick={onSalaryClick} label="薪资要求" placeholder="请选择薪资要求" />
          {/* 职位偏好 */}
          <General type='F_Preference' mustObj={data.mustObj} isNoWarp value={data.preference} onClick={onPreferenceClick} label="职位偏好" placeholder="请选择职位偏好" />
          <V style={{ height: `${btmHeight}px`, width: '100%' }}></V>
        </V>
        <ResumeFooter
          id="fotter"
          isDel={query.type == 2 && query.numMax > 1}
          publishRuleSwitch={publishRuleSwitch}
          btnTxt={data.subBtnTxt}
          onConfirm={onConfirm}
          onDelete={onDelete}
          isPdBtm
        />
      </V >
      <SalaryPicker
        visible={salaryPickerVis}
        type={data.positionType == 1 ? 'month' : 'day'}
        cancel={() => setSalaryPickerVis(false)}
        submit={onSalarySubmit}
        value={data.salaryObj.controlValues}
      />
      <ExpectPop
        visible={expectPopVis}
        occupations={data.occAreaReq ? data.occAreaReq.occupations || [] : []}
        salaryObj={data.salaryObj}
        cancel={() => setExpectPopVis(false)}
        confirm={onExpectConfirm}
      />
    </Page>
  )
}
