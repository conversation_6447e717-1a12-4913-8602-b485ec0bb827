import { View, ScrollView, Block } from '@tarojs/components'
import { useDidShow } from '@tarojs/taro'
import { useEffect, useRef, useState } from 'react'
import style from './index.module.scss'
import { useSelector } from '@/core/store/index'
import SearchHead from './components/SearchHead'
import { addOccSlted, delWodeItem, getBtmWodeHeight, getOccScrollPosition, getScrollViewHeight, getTopHeight, mayToastSelected, setIndScrollPositon, setOccScrollPositon } from './utils'
import IndItemView from './components/IndItemView'
import OneItemView from './components/OneItemView'
import TwoItemView from './components/TwoItemView'
import WodeSltedView from './components/WodeSltedView'
import { IClassifyDataRq } from '@/core/utils/index.d'
import Page from '@/components/Page'

import { sourceMap } from '@/core/utils/classify'
import useIsScreenFull from '@/hooks/useIsScreenFull'
import CustomNavbar from '@/components/CustomNavbar'

let landingTime: number

export default function Index() {
  const {
    value = [], // 默认选中的值,格式:[id:末级职位,industries:[行业ID(可为空,默认处理成第一个有职位的行业)]]
    maxSelectNum = 5, // 最多可选的数量
    title = '选择期望职位', // 标题
    isZpSingle = false, // 招聘类是否单选,并且订单类和招聘互斥
    isHideZpClassify = false, // 是否过滤招聘类职位
    isSelectToBack = false, // 最大可选数量为1是，直接返回选中值 true直接返回选中值
    maxSelectToastTxt = '',
    isClear = false, // 是否可以清空
  } = $.router.data || {} as IClassifyDataRq

  const [isScreenFull] = useIsScreenFull()

  const [topHeadHeight, setTopHeadHeight] = useState(0) // 顶部导航高度
  const [seachHeadHeight, setSeachHeadHeight] = useState(0) // 搜索框高度
  const [btmWodeHeight, setBtmWodeHeight] = useState(0) // 底部我的高度

  const [indSlted, setIndSlted] = useState('') // 选中的行业
  const [indScrollIntoView, setIndScrollIntoView] = useState('') // 行业滚动位置
  const [oneOccList, setOneOccList] = useState([]) // 行业对应的职位

  const [occSltedObj, setOccSltedObj] = useState<{ occSlted: any, occSltedArr: Array<string> }>({ occSlted: {}, occSltedArr: [] })
  const [occScrollIntoView, setOccScrollIntoView] = useState('') // 职位滚动位置
  const matchKeywordObj = useRef({}) // 职位对应的搜索词
  const [refreshSp, setRefreshSp] = useState(false) // 是否更新行业和职位的滚动位置

  const [sltedItemArr, setSltedItemArr] = useState([]) // 底部显示的我选择的职位
  const [modeObj, setModeObj] = useState({ mode: '', modeNum: maxSelectNum }) // 判断当前是订单类还是招聘类和数量限制

  // 原行业数组
  const industryTree = useSelector((state) => state.classify.industryTree)
  // 行业对应的一级职位数组
  const indOccIdOne = useSelector((state) => state.classify.indOccIdOne)
  // 一级职位对应的二级职位数组
  const occOneIdTwo = useSelector((state) => state.classify.occOneIdTwo)
  // 职位对象(格式:key:行业_职位ID,value:对象)
  const industryOccMap = useSelector((state) => state.classify.industryOccMap)

  useEffect(() => {
    getTopHeight(setTopHeadHeight)
    getScrollViewHeight(setSeachHeadHeight)
    getBtmWodeHeight(setBtmWodeHeight)
  }, [isScreenFull])

  useDidShow(() => {
    landingTime = Date.now()
  })

  useEffect(() => {
    initData()
  }, [industryTree])

  useEffect(() => {
    if (refreshSp) {
      setIndScrollPositon({ indId: indSlted }, { setIndScrollIntoView })
      getOccScrollPosition({ indId: indSlted, indOccIdOne, occSltedArr: occSltedObj.occSltedArr, occOneIdTwo }, { setOccScrollIntoView })
      setRefreshSp(false)
    }
  }, [refreshSp])

  useEffect(() => {
    const sltedItem = occSltedObj.occSltedArr.map(item => industryOccMap[item])
    const uniqueArr = sltedItem.reduce((slteds, item) => {
      if (slteds.findIndex(slted => `${slted.id}` === `${item.id}`) < 0) {
        const matchKeyword = matchKeywordObj.current[item.id]
        slteds.push({ ...item, ...(matchKeyword && item.name !== matchKeyword ? { matchKeyword: `(${matchKeyword})` } : {}) })
      }
      return slteds
    }, [])
    setSltedItemArr(uniqueArr)
    setTimeout(() => {
      getBtmWodeHeight(setBtmWodeHeight)
    }, 100)
  }, [occSltedObj])

  // 初始化数据
  const initData = () => {
    let nIndId = ''
    if ($.isArrayVal(value)) {
      const nOccSltedObj: { occSlted: any, occSltedArr: Array<string> } = { occSlted: {}, occSltedArr: [] }
      value.forEach((val) => {
        const { id, industries } = val
        if ($.isArrayVal(industries)) {
          industries.forEach((hindId) => {
            const occId = `${hindId}_${id}`
            const twoOccList = occOneIdTwo[occId]
            const item = industryOccMap[occId]
            if (item && !$.isArrayVal(twoOccList)) {
              addOccSlted(nOccSltedObj, occId, industryOccMap)
            } else {
              searchOcc(nOccSltedObj, id)
            }
          })
        } else {
          searchOcc(nOccSltedObj, id)
        }
      })
      if ($.isArrayVal(nOccSltedObj.occSltedArr)) {
        const industries = nOccSltedObj.occSltedArr.map((indOccId => `${indOccId.split('_')[0]}`))
        nIndId = industryTree.find((indId) => industries.includes(`${indId}`)) || ''
        setOccSltedObj(nOccSltedObj)
      } else {
        nIndId = industryTree[0] || ''
      }
    } else {
      nIndId = industryTree[0] || ''
    }
    setIndSlted(nIndId)
    setOneOccList(indOccIdOne[nIndId] || [])
    setRefreshSp(true)
  }

  // 如果在当前行业没有找到数据，从所有行业中搜索并保存到已选中值中
  const searchOcc = (nOccSltedObj, id) => {
    industryTree.find((indId) => {
      const occId = `${indId}_${id}`
      const twoOccList = occOneIdTwo[occId]
      if (industryOccMap[occId] && !$.isArrayVal(twoOccList)) {
        addOccSlted(nOccSltedObj, occId, industryOccMap)
        return true
      }
      return false
    })
  }

  // 行业点击选择
  const onIndustryClick = (indId) => {
    if (`${indId}` === `${indSlted}`) return
    setIndSlted(indId)
    setOneOccList(indOccIdOne[indId] || [])
    setTimeout(() => {
      getOccScrollPosition({ indId, indOccIdOne, occSltedArr: occSltedObj.occSltedArr, occOneIdTwo }, { setOccScrollIntoView })
      getBtmWodeHeight(setBtmWodeHeight)
    }, 50)
  }

  // 末级职位点击选择
  const onOccClick = (indOccId, ext?) => {
    if (isSelectToBack && maxSelectNum == 1) {
      onSelectToBack(indOccId)
      return
    }
    const { isToast = true } = ext || {}
    const occItem = industryOccMap[indOccId]
    const { mode: iMode } = occItem || {}
    const { nOccSltedObj, nModeNum } = handleZpSingleLogic(`${iMode}`)

    if (nOccSltedObj.occSlted[indOccId]) {
      delete nOccSltedObj.occSlted[indOccId]
      const idx = nOccSltedObj.occSltedArr.findIndex(osid => `${osid}` === `${indOccId}`)
      nOccSltedObj.occSltedArr.splice(idx, 1)
    } else {
      if ((`${iMode}` === '1' || !isZpSingle) && sltedItemArr.length >= nModeNum) {
        $.msg(`最多选择${nModeNum}个${maxSelectToastTxt}职位`)
        return
      }
      nOccSltedObj.occSlted[indOccId] = indOccId
      nOccSltedObj.occSltedArr.push(`${indOccId}`)
      isZpSingle && isToast && mayToastSelected({ item: occItem, sltedItemArr, isZpSingle })
    }

    setOccSltedObj(nOccSltedObj)
  }
  // 判断订单类或者招聘类和职位数量限制
  const handleZpSingleLogic = (iMode: string) => {
    const nOccSltedObj = { ...occSltedObj }
    let nModeNum = modeObj.modeNum
    const mode = `${modeObj.mode}`
    if (isZpSingle) {
      if (mode !== iMode || iMode === '2') {
        nOccSltedObj.occSlted = {}
        nOccSltedObj.occSltedArr = []
      }
      if (iMode === '1') {
        nModeNum = maxSelectNum
      }
      if (iMode === '2') {
        nModeNum = 1
      }
      if (mode !== iMode) {
        setModeObj({ mode: iMode, modeNum: nModeNum })
      }
    }
    return { nOccSltedObj, nModeNum }
  }

  const onWodeItemDel = (item) => {
    const { id } = item
    if (matchKeywordObj.current[id]) {
      delete matchKeywordObj.current[id]
    }
    delWodeItem(item, { ...occSltedObj }, { setOccSltedObj })
  }

  const onClear = () => {
    setOccSltedObj({ occSlted: {}, occSltedArr: [] })
  }

  const onXunClick = (occId) => {
    const occItem = industryOccMap[occId]
    const { id } = occItem || {}
    if (id) {
      onIndustryClick(id)
      setIndScrollPositon({ indId: id }, { setIndScrollIntoView })
    }
  }

  const onSearchClick = (item) => {
    const { occId, indId, matchKeyword } = item || {}
    const indOccId = `${indId}_${occId}`
    if (isSelectToBack && maxSelectNum == 1) {
      onSelectToBack(indOccId)
      return
    }
    if (!matchKeywordObj.current[occId]) {
      matchKeywordObj.current[occId] = matchKeyword
    }
    if (!occSltedObj.occSlted[indOccId]) {
      onOccClick(indOccId, { isToast: false })
    }
    setIndSlted(indId)
    setOneOccList(indOccIdOne[indId] || [])
    setTimeout(() => {
      const occ = industryOccMap[indOccId]
      const { pid } = occ || {}
      setIndScrollPositon({ indId }, { setIndScrollIntoView })
      setOccScrollPositon({ indId, occId: `${pid}` === `${indId}` ? indId : pid }, { setOccScrollIntoView })
    }, 50)
  }

  const onConfirm = () => {
    const data = [...occSltedObj.occSltedArr]
    const rtData: Array<{ id: string, industries: Array<string>, name: string, mode: number }> = []
    if ($.isArrayVal(data)) {
      data.forEach((indOccId) => {
        const [indId, occId] = indOccId.split('_')
        const idx = rtData.findIndex((item: any) => `${item.id}` === `${occId}`)
        if (idx >= 0) {
          const item = rtData[idx]
          item.industries.push(`${indId}`)
        } else {
          const item = industryOccMap[indOccId]
          rtData.push({ id: `${occId}`, industries: [`${indId}`], name: item.name, mode: item.mode })
        }
      })
    }
    reportSubmitSelection(rtData)
    $.router.event(rtData)
    $.router.back(1)
  }

  // 选中末级职位后直接返回上一页
  const onSelectToBack = (indOccId) => {
    const item = industryOccMap[indOccId]
    const [indId, occId] = indOccId.split('_')
    reportSubmitSelection([{ id: occId, industries: [indId], name: item.name, mode: item.mode }])
    $.router.event([{ id: `${occId}`, industries: [`${indId}`], name: item.name, mode: item.mode }])
    $.router.back(1)
  }

  return (
    <Page backgroundColor='#fff'>
      <View id='top-head'>
        <CustomNavbar isBack title={title} />
      </View>
      <View className={style.page} style={{ height: `calc(100vh - ${topHeadHeight}px)` }}>
        <View className={style.content} >
          <View id='search-head'>
            <SearchHead sltedItemArr={sltedItemArr} modeObj={modeObj} topHeadHeight={topHeadHeight} isHideZpClassify={isHideZpClassify} onSearchClick={onSearchClick} maxSelectToastTxt={maxSelectToastTxt} isZpSingle={isZpSingle} />
          </View>
          <View className={style.scrollViewOut} style={{ height: `calc(100vh - ${seachHeadHeight + btmWodeHeight + topHeadHeight}px)` }}>
            <ScrollView scrollIntoView={indScrollIntoView} className={style.leftScrollView} scroll-y >
              {
                industryTree.map((ind) => {
                  return <IndItemView occId={ind} name={industryOccMap[ind].name} onClick={onIndustryClick} slted={indSlted} key={ind} id={`ypClass_${ind}`} />
                })
              }
            </ScrollView>
            <ScrollView scrollIntoView={occScrollIntoView} className={style.rightScrollView} scroll-y style={{ height: `calc(100vh - ${seachHeadHeight + btmWodeHeight + topHeadHeight}px)` }}>
              {
                oneOccList.map((oneocc) => {
                  return (
                    <Block key={oneocc}>
                      <OneItemView onClick={onXunClick} indSlted={indSlted} item={industryOccMap[`${indSlted}_${oneocc}`] || {}} occId={oneocc} id={`ypClass_${indSlted}_${oneocc}`} />
                      <TwoItemView
                        oneItem={industryOccMap[`${indSlted}_${oneocc}`] || {}}
                        list={occOneIdTwo[`${indSlted}_${oneocc}`] || []}
                        oneocc={oneocc}
                        indSlted={indSlted}
                        occSlted={occSltedObj.occSlted}
                        onClick={onOccClick}
                      />
                    </Block>
                  )
                })
              }
            </ScrollView>
          </View>
          <View className={style.fotter} id='fotter'>
            <View>
              {
                sltedItemArr.length > 0 || (isClear && value.length)
                  ? <WodeSltedView sltedItemArr={sltedItemArr} maxNum={modeObj.modeNum} onClick={onWodeItemDel} onClear={onClear} onConfirm={onConfirm} />
                  : null
              }
            </View>
            <View className={style.btmHt}></View>
          </View>
        </View>
      </View >
    </Page>
  )
}

const reportSubmitSelection = (items: any[], reportTime = Date.now()) => {
  const workTypeV2 = items?.map(item => `${item.industries?.[0] || -1}_${item.id}`).join(',')
  const { source_id: sourceId } = $.router.data || {}
  const source = sourceMap[sourceId] || ''
  $.report.event('workTypeSelection', {
    filter_results: items?.length ? '成功' : '失败',
    residencetime: `${((reportTime - landingTime) / 1000).toFixed(1)}s`,
    source,
    work_type_v2: workTypeV2,
  })
}
