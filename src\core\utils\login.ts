import { useCallback, useEffect, useRef, useState } from 'react'
import { actions, dispatch, useSelector } from '../store'
import { afterLogin } from '@/utils/login'

/**
 * @description 全局的登录状态
 * @returns Promise<undefined>
 */
export const login = () => {
  return new Promise((resolve, reject) => {
    const currentPage = $.taro.getCurrentPages().pop() || {}
    dispatch(actions.global.setState({ showLoginModel: {
      pageId: currentPage.__wxWebviewId__,
      success: () => {
        resolve(undefined)
        dispatch(actions.global.setState({ showLoginModel: {} }))
      },
      fail: () => {
        reject()
        dispatch(actions.global.setState({ showLoginModel: {} }))
      },
    } }))
  })
}

/**
 * 解决list 并行获取authCode问题
 */
export const getAuthCodeLock = (() => {
  let promise
  return () => {
    if (!promise) {
      promise = $.taro.login().catch((e) => { promise = undefined; throw e })
    }
    return promise
  }
})()

/**
 *
 * @param param0
 * @returns
 */
export const useLogin = ({ success, fail, disableSuccessMsg = false }: UseLoginProps) => {
  const authCodeRef = useRef<string>('')
  const token = useSelector((state) => state.storage.token)
  useEffect(() => {
    if (token) return
    getAuthCodeLock().then(res => {
      if (res.code) {
        authCodeRef.current = res.code
      }
    })
  }, [token])

  const toLoginPage = () => {
    if ($.router.path.includes('pages/auth-login/index')) return
    $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
      if (logged) {
        success && success()
      } else {
        fail && fail()
      }
    })
  }

  const fakerLogin = useCallback(async (evt: any) => {
    const { iv, encryptedData } = evt.detail || {}
    if (!iv || !encryptedData) {
      await $.msg('获取手机号失败，将为你跳转到验证码登陆')
      toLoginPage()
    } else {
      try {
        $.showLoading('登陆中...')
        const result = await $.taro.login()
        if (result && result.code) {
          const response = await $.request['POST/account/v1/login/kuaiShouMiniLogin']({ authCode: result.code, encryptedData, iv, appId: $.config.appid })
          const token = $.getObjVal(response, '0.token')
          const message = $.getObjVal(response, '1.message')
          $.hideLoading()
          if (token) {
            afterLogin(token)
            !disableSuccessMsg && $.msg('登录成功')
            success && success()
          } else {
            fail && fail()
            $.msg(message || '登陆失败')
          }
        }
      } catch ([_, error]) {
        $.hideLoading()
        if (error && (error.code == ******** || error.code == ********)) {
          fail && fail()
          await $.msg('无法获取支付宝绑定手机号,为您选择手机号验证码登录')
          $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
            if (logged) {
              success && success()
              !disableSuccessMsg && $.msg('登录成功')
            } else {
              fail && fail()
            }
          })
          return
        }

        const dialogIdentify = $.getObjVal(error, 'data.dialogData.dialogIdentify') || $.getObjVal(error, 'data.data.dialogData.dialogIdentify')
        if (dialogIdentify === 'limitOperate' || (error && error.code == 10110009)) {
          $.confirm({
            title: '温馨提示',
            content: '您的登录/注册环境异常，请稍后再试',
            confirmText: '联系客服',
            cancelText: '知道了',
          }).then(() => {
            $.taro.makePhoneCall({ phoneNumber: '4008381888' })
          })
          fail && fail()
          return
        }
        // fail && fail()
        $.msg('登录失败, 请重试')
      }
    }
  }, [])
  /** openType="getPhoneNumber" onGetPhoneNumber={fakerLogin} */
  return [{ openType: 'getPhoneNumber', onGetPhoneNumber: fakerLogin }] as const
}

type UseLoginProps = {
  success?: () => void;
  fail?: () => void;
  disableSuccessMsg?: boolean
}
