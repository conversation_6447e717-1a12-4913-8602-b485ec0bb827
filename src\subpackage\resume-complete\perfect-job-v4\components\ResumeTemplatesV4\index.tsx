import { Block } from '@tarojs/components'
import { useEffect, useRef, useState } from 'react'
import InputWidget from '../InputWidget'
import FormCheckbox from '../FormCheckbox'

type IResumeTemplatesV4Props = {
  /** 控件列表 [{status: 是否隐藏控件 1.显示}] */
  controlList?: Array<any>,
  /** 控件对象 */
  controlObj?: any,
  /** 模板的code */
  templateCode?: string,
  query?: any,
  change?: (e: any) => void
}

// 输入框类型
const typeCodes = ['INPUT_STRING', 'INPUT_NUMBER', 'INPUT_WITH_UNIT']
/** 期望职位 */
const ResumeTemplatesV4 = (props: IResumeTemplatesV4Props) => {
  const { controlList = [], controlObj = {}, change } = props

  const [data, setData] = useState<any>({
    labelSlted: {},
    controlObj: {},
  })
  const list = useRef({})

  useEffect(() => {
    if ($.isArrayVal(controlList)) {
      const nControlObj = { ...(controlObj || {}), ...(data.controlObj || {}) }
      const nList = {}
      controlList.forEach((cl) => {
        const { code, cObj } = cl || {}
        const item = nControlObj[code]
        nList[code] = item
        const { controlAttr } = item || {}
        const { dataObj, labelList } = controlAttr || {}
        const { ifMulti, limitNum } = dataObj || {}
        if ((!ifMulti || limitNum == 1) && $.isArrayVal(labelList)) {
          const checkedLabel = labelList.find(({ checked }) => checked)
          const { code: lcode } = checkedLabel || {}
          const cArr = $.isEmptyObject(cObj) ? [] : (cObj[lcode] || [])
          if ($.isArrayVal(cArr)) {
            cArr.forEach((cCode) => {
              nList[cCode] = nControlObj[cCode]
            })
          }
        }
      })
      list.current = nList
      setData((prev) => ({ ...prev, controlObj: nControlObj }))
      handleLabelSlted(nList)
      change && change({ list: nList })
    }
  }, [controlList])

  // 处理标签选中的值
  const handleLabelSlted = (list) => {
    const labelSlted: any = {}
    const arr = Object.values(list || {})
    arr.forEach(it => {
      const { controlAttr, controlCode } = it || {} as any
      const { labelList, dataObj } = controlAttr || {}
      const { ifMulti, limitNum } = dataObj || {}
      if ((!ifMulti || limitNum == 1) && $.isArrayVal(labelList)) {
        const cArr = labelList.filter(lb => lb.checked)
        if (cArr.length == 1) {
          const { code } = cArr[0] || {}
          labelSlted[controlCode] = code
        } else {
          labelSlted[controlCode] = ''
        }
      }
    })
    setData((prev) => ({ ...prev, labelSlted }))
  }
  const onChange = (value) => {
    const nList = { ...(list.current) }
    const { labelSlted } = data
    const nControlObj:any = { ...(data.controlObj || {}) }
    const { dataSource, code, item } = value || {}

    nList[code] = dataSource
    nControlObj[code] = dataSource
    const sltedCode = labelSlted[code]
    if (sltedCode && !$.isEmptyObject(item.cObj) && item.cObj[sltedCode]) {
      const cArr = item.cObj[sltedCode]
      cArr.forEach(ccode => {
        const control = nControlObj[ccode]
        if (control) {
          const { controlAttr, inputValue } = control
          const { labelList } = controlAttr || {}
          if ($.isArrayVal(labelList)) {
            const nlabelList = labelList.map(l => ({ ...l, checked: false }))
            nControlObj[ccode].controlAttr.labelList = nlabelList
          } else if (inputValue) {
            nControlObj[ccode].inputValue = ''
          }
        }
        delete nList[ccode]
      })
    }

    const { controlAttr } = dataSource || {}
    const { dataObj, labelList } = controlAttr || {}
    const { ifMulti, limitNum } = dataObj || {}
    if (!$.isEmptyObject(item.cObj) && (!ifMulti || limitNum == 1) && $.isArrayVal(labelList)) {
      const checkedLabel = labelList.find(({ checked }) => checked)
      const { code: lcode } = checkedLabel || {}
      const cArr = item.cObj[lcode] || []
      if ($.isArrayVal(cArr)) {
        cArr.forEach((cCode) => {
          nList[cCode] = data.controlObj[cCode]
        })
      }
    }

    list.current = nList
    // this.setData({ list, controlObj: nControlObj, hideRedBd: { ...hideRedBd, [code]: true } })
    setData((prev) => ({ ...prev, controlObj: nControlObj }))
    handleLabelSlted(nList)
    change && change({ list: nList })
  }

  return (
    <Block>
      {
        (controlList || []).map((item) => {
          const { code } = item
          const control = data.controlObj[code] || {}
          const { controlTypeCode } = control
          if (typeCodes.includes(controlTypeCode)) {
            return (
              <InputWidget
                key={code}
                control={control}
                change={(value) => onChange({ dataSource: value, code, item })}
              />
            )
          }

          const checkBoxArr:Array<any> = []
          checkBoxArr.push(<FormCheckbox
            key={code}
            control={control}
            change={(value) => onChange({ dataSource: value, code, item })}
          />)
          const slted = data.labelSlted[item.code]

          if (slted && item.cObj[slted] && item.cObj[slted].length > 0) {
            item.cObj[slted].forEach((cCode) => {
              const cControl = data.controlObj[cCode] || {}
              const { controlTypeCode: cControlTypeCode } = cControl || {}
              if (cControl && cControl.status == 1) {
                if (typeCodes.includes(cControlTypeCode)) {
                  checkBoxArr.push(
                    <InputWidget
                      key={cCode}
                      control={cControl}
                      change={(value) => onChange({ dataSource: value, code: cCode, item })}
                    />,
                  )
                } else {
                  checkBoxArr.push(
                    <FormCheckbox
                      key={cCode}
                      control={cControl}
                      change={(value) => onChange({ dataSource: value, code: cCode, item })}
                    />,
                  )
                }
              }
            })
          }
          return checkBoxArr
        })
      }
    </Block>
  )
}

export default ResumeTemplatesV4
