import Taro from '@tarojs/taro'
import storage from '@/store/storage/storage'

/** 发布简历缓存用户选择的数据 */
export const savePublishData = (ky, val) => {
  const pubishData:any = { ...(storage.getItemSync('pubishData') || {}) }
  pubishData[ky] = val
  storage.setItemSync('pubishData', pubishData)
}

// 完善流程一用到控件
export const fstStepCodes = ['F_Name', 'F_Gender', 'F_Age']
// 完善流程二用到控件
export const secStepCodes = ['F_WorkType', 'F_WorkNature', 'F_SalaryMonth', 'F_Preference']
/**
 * @description 判断发布跳转逻辑
 * @param template 模板控件
 * @param userInfo 用户信息
 *  */
// eslint-disable-next-line sonarjs/cognitive-complexity
export const judgePublishLogic = (template, userInfo) => {
  const { realNameStatus, userName, gender, birthday, nameAuditStatus } = userInfo || {}
  const publishFlow = { subStep: 1, perfects: [1, 1] }
  const { templateInfo } = template || {}
  const { controlInfoList } = templateInfo || {}
  if (['1', '2'].includes(`${realNameStatus}`)) { // 判断是否已实名，已实名完善流程一不跳转
    publishFlow.perfects[0] = 0
  }

  if (Array.isArray(controlInfoList)) {
    // -----判断完善流程一是否有必填-----
    // 是否跳转完善流程一
    let isOkFstStep = false
    controlInfoList.forEach((ct) => {
      const { status, controlCode, scenes } = ct || {}
      if (status == 1 && fstStepCodes.includes(controlCode)) {
        isOkFstStep = true
        const scene = ($.isArrayVal(scenes) ? scenes : []).find((s) => s.displayScene == 'RESUME_PUBLISH_DISPLAY')
        const { ifMust } = scene || {}
        if (ifMust && publishFlow.perfects[0] != 0) {
          publishFlow.subStep = 2
        }
      }
    })
    !isOkFstStep && (publishFlow.perfects[0] = 0)

    // -----判断完善流程二是否有必填-----
    // 是否跳转完善流程二
    let isOkSecStep = false
    controlInfoList.forEach(ct => {
      const { status, controlCode, scenes } = ct || {}
      if (status == 1 && secStepCodes.includes(controlCode)) {
        isOkSecStep = true
        const scene = ($.isArrayVal(scenes) ? scenes : []).find((s) => s.displayScene == 'RESUME_PUBLISH_DISPLAY')
        const { ifMust } = scene || {}
        if (ifMust) {
          publishFlow.subStep = 3
        }
      }
    })

    !isOkSecStep && (publishFlow.perfects[1] = 0)
    /**
      *  1）用户已实名或字段已全部完善不展示此页面，直接进入下一步完善
      *  2）若用户未实名，完善了某项内容，该项内容在页面内做回显，支持用户修改
      *  3）个人信息页已完善过内容，此页面直接跳过，直接进入下一步完善，若用户有一项未完善需要跳此页面，将已完善的内容默认填充
      *  特殊场景：如果用户完成了实名认证，但是实名异常（没有姓名、性别、出生年月任意一项），用户会进入引导完善页面1，完善个人信息
     */
    if (isOkFstStep && (!userName || (userName && ['先生', '女士'].includes(userName) && nameAuditStatus != 1) || !gender || !birthday)) {
      publishFlow.perfects[0] = 1
    } else if (userName && gender && birthday) {
      publishFlow.perfects[0] = 0
      if (publishFlow.subStep == 2) {
        publishFlow.subStep = 1
      }
    }
  } else {
    publishFlow.perfects[0] = 0
    publishFlow.perfects[1] = 0
  }

  if (publishFlow.perfects[0] == 0 && publishFlow.perfects[1] == 0) {
    publishFlow.subStep = 1
  }
  return publishFlow
}

/** 组装模板dataList的数据 */
export const handleDataList = (dataList = []) => {
  const dataObj: any = {}
  if ($.isArrayVal(dataList)) {
    dataList.forEach((dl:any) => {
      let val = dl.value
      switch (dl.code) {
      case 'min':
      case 'max':
      case 'precision':
      case 'limitNum':
        val = Number(val)
        break
      case 'ifMulti':
        val = Boolean(val)
        break
      default:
      }
      dataObj[dl.code] = val
    })
  }
  return dataObj
}

/**
 * 数字数组的方法，开始，结束，间隔
 * 是否包含结束值-默认不包含
 */
function generateArr(start: number, end: number, interval: number, includeEnd = false) {
  const arr:Array<any> = []
  for (let i = start; i < end; i += interval) {
    arr.push(i)
  }
  if (includeEnd) {
    arr.push(end)
  }
  return arr
}

/** 月薪数据 */
export function getMonthData() {
  const stageOne = generateArr(1000, 30000, 1000)
  const stageTwo = generateArr(30000, 100000, 5000)
  const stageThree = generateArr(100000, 260000, 10000, true)
  return ['面议'].concat(stageOne, stageTwo, stageThree)
}

/** 日薪数据 */
export function getDayData() {
  const stage = generateArr(100, 2100, 100, true)
  return ['面议'].concat(stage)
}

/** 薪资转换文本
 * 日结1  月结2   计量3  面议4
 * 元/日    1,100,200,1
 * 元/月    2,100,200,2
 * 元/件    3,100,200,6
 * 元/平方  3,100,200,7
 * 元/立方  3,100,200,8
 */
export function getSalaryText(salary: string, moneyType = 'month'): string {
  const arr = salary.split(',')
  const type = arr[0]

  if (arr.length < 2 || type === '4') {
    return '面议'
  }
  if (arr[1] == '0' || arr[2] == '0') {
    return '面议'
  }

  const moneyMin = parseFloat(arr[1] || '0')
  const moneyMax = parseFloat(arr[2] || '0')
  const unitType = arr[3] || ''
  let unit = moneyType === 'day' ? '元/天' : '元'

  if (type === '3') {
    switch (unitType) {
    case '6':
      unit = '元/件'
      break
    case '7':
      unit = '元/平方'
      break
    case '8':
      unit = '元/立方'
      break
    default:
    }
  }

  const formatMoney = (money: number, isUnit = true, isFormat = true) => {
    let nUnit = isUnit ? unit : ''
    if (isFormat && money >= 10000) {
      nUnit = isUnit ? `万${unit}` : ''
      return `${+(money / 10000).toFixed(2)}${nUnit}`
    }
    return `${money}${nUnit}`
  }

  if (moneyMin && moneyMax) {
    if (moneyMin == moneyMax) { // 两个值相等时
      return formatMoney(moneyMin)
    }
    const isFormat = moneyMin >= 10000 && moneyMax >= 10000
    return `${formatMoney(moneyMin, false, isFormat)}-${formatMoney(moneyMax, true, isFormat)}`
  }

  const moneyVal = moneyMin || moneyMax
  return formatMoney(moneyVal)
}

/** 获取控件数据 */
export function getListValue(options) {
  const { list, reqAll = false, notIfMust = false, isHideMsg = false, templateCode = '', mustKey } = options
  let value:Array<any> = [] // 提交的值
  let isErrMsg = false // 是否有错误信息提示
  for (let i = 0; i < list.length; i += 1) {
    const item = list[i]
    /** 当控件没有被隐藏的时候才去判断 */
    const { value: subValue, message } = handlerValueBool({ item, reqAll, notIfMust, templateCode, mustKey })
    if (!subValue) { // 如果验证失败，就跳出循环
      value = []
      isErrMsg = true
      if (!isHideMsg && message) {
        $.msg(message)
      }
      break
    } else if (subValue.controlValues) {
      value.push(subValue)
    }
  }
  return { value, isErrMsg }
}

/** 处理和判断数据值 */
// eslint-disable-next-line sonarjs/cognitive-complexity
function handlerValueBool(options): {value, message?: string} {
  const { item, reqAll = false, notIfMust = false, templateCode = '', mustKey } = options
  const { controlAttr, controlTypeCode, inputValue, controlCode, controlName, controlNatureCode, orderAsc } = item || {}
  const { labelList, dataObj } = controlAttr || {}
  const value: any = {
    templateCode,
    controlCode,
    controlTypeCode,
    controlName,
    controlNatureCode,
    orderAsc,
  }
  let message = ''
  switch (controlTypeCode) {
  /** INPUT_STRING:字符输入框控件 INPUT_NUMBER:数字输入框控件  INPUT_WITH_UNIT:带单位输入框控件 */
  case 'INPUT_STRING':
  case 'INPUT_NUMBER':
  case 'INPUT_WITH_UNIT':
    if ($.isEmptyObject(inputValue)) {
      value.controlValues = ''
    } else {
      value.controlValues = item.inputValue
      if (dataObj && dataObj.unit) {
        value.unit = dataObj.unit
      }
    }
    break
    /* LABEL:标签控件  POPUP_LABEL:弹窗标签控件 */
  case 'LABEL':
  case 'POPUP_LABEL':
    if ($.isArrayVal(labelList)) {
      value.controlValues = labelList.filter(item => item.checked).map(item => `${item.code}`).join(',')
      value.controlNames = labelList.filter(item => item.checked).map(item => `${item.name}`).join('、')
    } else {
      value.controlValues = ''
    }
    break
  default:
  }
  // 是否有值
  const isValue = !!value.controlValues
  /** 是否必选 */
  const required = item.status == 1 && !notIfMust && (item[mustKey] || reqAll)
  if (required && !isValue) {
    // 必填
    message = `请完善${item.controlName}`
    return { value: false, message }
  }
  if (isValue && item.status == 1) { // 如果有值,并且组件没有被隐藏
    let numRes
    switch (controlTypeCode) {
    case 'INPUT_STRING':
    case 'INPUT_NUMBER':
    case 'INPUT_WITH_UNIT':
      // 数字类型
      numRes = isNumberValue(item, item.inputValue)
      break
    default:
    }
    if (numRes && numRes.error) {
      return { value: false, message: numRes.message }
    }
  }

  return { value, message: '' }
}

/** 判断输入的数字是否合法 */
function isNumberValue(control, value: string | number): {error: boolean, message?: string} {
  const { controlAttr, controlName } = control || {}
  const { dataObj } = controlAttr || {}
  const { precision, min, max } = dataObj || {}
  const valMin = Number(min) || 0 // 最小值
  const valMax = Number(max) || 0 // 最大值
  const valPrecision = Number(precision) || 0 // 允许小数点位数
  const val = Number(value)
  if (Number.isNaN(val)) { // 是否是数字
    return { error: true, message: `输入的${controlName}不合法` }
  }
  if (valMin > 0 && valMax > 0 && !$.isVaildNum(val, valMin, valMax)) { // 最小值不能大于最大值
    return { error: true, message: `请输入${valMin}-${valMax}内的${controlName}` }
  }
  if (valMin > 0 && val < valMin) { // 最小值
    return { error: true, message: `输入的${controlName}不能小于${valMin}` }
  }
  if (valMax > 0 && val > valMax) { // 最大值
    return { error: true, message: `输入的${controlName}不能大于${valMax}` }
  }
  // 正则判断小数点位数
  if (valPrecision > 0 && !isDecimal(value, valPrecision)) {
    return { error: true, message: `输入的${controlName}小数点位数不能超过${valPrecision}位` }
  }
  return { error: false }
}

/** 正则判断小数点位数
 * @param value 需要验证的数字
 * @param decimal 小数点位数 默认0
 */
const isDecimal = (value: number|string, decimal = 0) => {
  const reg = new RegExp(`^\\d+(\\.\\d{1,${decimal}})?$`)
  return reg.test(`${value}`)
}

// 发布流程返回结果处理
export const handlePublishResult = (res, sucTxt = '发布') => {
  const { code, error, data, message } = res || {}
  if (code != 0 || error) {
    if (!data && message && message.indexOf('请求成功') < 0) {
      $.msg(message)
      return true
    }
    $.confirm({
      title: '温馨提示',
      content: `简历${sucTxt}失败，请联系客服查询具体原因************`,
      cancelText: '取消',
      confirmText: '联系客服',
    }).then(() => {
      Taro.makePhoneCall({ phoneNumber: '4008381888' })
    })
    return true
  }
  return false
}
