.item {
  padding: 32rpx 0;
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
  line-height: 54rpx;
}

.must {
  font-size: 28rpx;
  color: rgba(232, 54, 46, 1);
  line-height: 40rpx;
}

.dtpView {
  margin-top: 24rpx;
  width: 100%;
}

.pickerView {
  width: 100%;
  height: 416rpx;
}

.indicatorClass {
  color: rgba(0, 0, 0, 0.65);
  font-size: 38rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 104rpx !important;
  line-height: 54rpx;
}

.colClass {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  color: rgba(0, 0, 0, 0.65);
  width: 343rpx;
  height: 104rpx !important;
  line-height: 54rpx;
}
