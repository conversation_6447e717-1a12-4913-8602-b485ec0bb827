/*
 * @Date: 2024-11-22 14:26:59
 * @Description: 筛选器
 */

import cn from 'classnames'
import { useLoad } from '@tarojs/taro'
import { View as V, Text as T } from '@tarojs/components'
import s from './index.module.scss'
import Click from '@/components/Click'
import { actions, dispatch, getState, store } from '@/core/store'
import { getAreaByAdcode, getLocationByApi } from '@/utils/location'

const list = [
  { label: '综合', value: 2 },
  { label: '最新', value: 1 },
  { label: '附近', value: 3 },
]

export default ({ listType, setListType, onRefResh, homeLocation, homeNearbyLocation }) => {
  const onChange = (item) => {
    setListType(item.value)
    // 如果是定位
    if (item.value == 3) {
      $.showLoading('定位中...')
      $.taro.getLocation({
        type: 'gcj02',
        success: async (res) => {
          const { addr } = await getLocationByApi(res.longitude, res.latitude)
          const { province, city, district, current: area } = await (await getAreaByAdcode(addr.adcode)) || {}
          const { homeLocation } = store.getState().storage
          const nextHomeNearbyLocation = {
            success: true,
            data: {
              latitude: res.latitude,
              longitude: res.longitude,
              areaId: area.id,
              name: addr.streetNumber?.street || area.name,
            },
          }
          const userLocation = {
            success: true,
            province,
            city,
            district,
            ...area,
            longitude: res.longitude,
            latitude: res.latitude,
          }

          if (!homeLocation.success && (!homeLocation?.data?.areaId || homeLocation.data.areaId == 1)) {
            const homeLocation = {
              success: true,
              data: {
                latitude: res.latitude,
                longitude: res.longitude,
                areaId: area.id,
                name: area.name,
              },
            }
            dispatch(actions.storage.setItem({ key: 'homeLocation', value: homeLocation }))
          }
          dispatch(actions.storage.setItem({ key: 'homeNearbyLocation', value: nextHomeNearbyLocation }))
          dispatch(actions.storage.setItem({ key: 'userLocation', value: userLocation }))
          onRefResh({ page: 1 }, { nextHomeNearbyLocation, nextListType: item.value })
        },
        fail: (e) => {
          const nextHomeNearbyLocation = {
            success: false,
            data: {
              latitude: null,
              longitude: null,
            },
          }
          dispatch(actions.storage.setItem({ key: 'homeNearbyLocation', value: nextHomeNearbyLocation }))
          onRefResh({ page: 1 }, { nextHomeNearbyLocation, nextListType: item.value })
        },
        complete: () => {
          $.hideLoading()
        },
      } as any)
    } else {
      onRefResh({ page: 1 }, { nextListType: item.value })
    }
  }

  /** 选择地址 */
  const onChooseCity = () => {
    // 埋点
    $.report.event('city_filter_search_click', {
      source_id: '1',
      source: '招工列表',
    })
    $.openAddress({
      areas: [homeLocation.data.areaId],
      disabledIds: [33, 34, 35],
      showLocation: true,
    }, { button_name: homeLocation.data.name, source_id: '1', source: '招工列表' }, async ([res]) => {
      const { data: { latitude, longitude }, success } = getState().storage.homeLocation
      const nextHomeLocation = {
        success,
        data: {
          longitude,
          latitude,
          areaId: res.id,
          name: res.name,
        },
      }

      $.report.event('city_filter_search_submit', {
        source_id: '1',
        source: '招工列表',
        search_area_name: res.name || '',
      })

      dispatch(actions.storage.setItem({ key: 'homeLocation', value: nextHomeLocation }))
      onRefResh({ page: 1 }, { nextHomeLocation })
    })
  }

  /** 选择附近 */
  const onChooseLocation = () => {
    $.taro.chooseLocation({
      latitude: homeNearbyLocation.data.latitude,
      longitude: homeNearbyLocation.data.longitude,
      async success(res: any) {
        const { addr } = await getLocationByApi(res.longitude, res.latitude)
        const { current: area } = await (await getAreaByAdcode(addr.adcode)) || {}
        const nextHomeNearbyLocation = {
          success: true,
          data: {
            latitude: String(res.latitude),
            longitude: String(res.longitude),
            areaId: area.id,
            name: addr.streetNumber?.street?.length ? addr.streetNumber?.street : area.name,
          },
        }
        dispatch(actions.storage.setItem({ key: 'homeNearbyLocation', value: nextHomeNearbyLocation }))
        onRefResh({ page: 1 }, { nextHomeNearbyLocation })
      },
      fail(e: any) {
        if (e.errCode === 111) {
          return
        }
        // 用户取消
        $.msg('定位失败，请确认系统定位权限是否开启')
      },
    })
  }

  useLoad(() => {
    const { listType } = $.router.query || {}
    if (listType == 3) {
      onChange({ label: '附近', value: 3 })
    }
  })

  const nearbyName = homeNearbyLocation.data?.name || '定位中'
  const homeName = homeLocation.data?.name || '全国'

  return (
    <V className={s.body}>
      <V className={s.box}>
        {list.map((item) => {
          return (
            <Click
              onClick={() => onChange(item)}
              key={item.value}
              className={cn(s.item, item.value === listType && s.active)}
            >
              {item.label}
            </Click>
          )
        })}
      </V>
      {/* 选择城市 */}
      {listType !== 3 ? (
        <Click onClick={onChooseCity} className={s.city}>
          <T className={s.cityText}>{homeName.length > 8 ? `${homeName.slice(0, 8)}...` : homeName}</T>
        </Click>
      ) : null}
      {/* 附近位置 */}
      {listType === 3 ? (
        <Click onClick={onChooseLocation} className={s.city}>
          {homeNearbyLocation.success === null ? <T className={s.cityText}>定位中</T> : null}
          {homeNearbyLocation.success === false ? <T className={s.cityText}>定位失败</T> : null}
          {homeNearbyLocation.success ? <T className={s.cityText}>{nearbyName.length > 8 ? `${nearbyName.slice(0, 8)}...` : nearbyName}</T> : null}
        </Click>
      ) : null}
    </V>
  )
}
