/*
 * @Date: 2024-11-18 09:47:42
 * @Description: 岗位详情
 */

import { Image, View } from '@tarojs/components'
import { useMemo, useState } from 'react'
import classNames from 'classnames'
import Skeleton from './components/Skeleton'
import styles from './index.module.scss'
import { HeaderCard } from './components/HeaderCard'
import EmployerCard from './components/EmployerCard'
import ContentCard from './components/ContentCard'
import CompanyAddressMap from './components/CompanyAddressMap'
import YpSecurityCard from './components/YpSecurityCard'
import Page from '@/components/Page'
import {
  FINANCE_SCALE_MAP,
  getLabelByValue,
  queryJobDetail,
  STAFF_SIZE_MAP,
  uploadStatisticsData,
} from '@/utils/helper/recruit'
import { delNullOp } from '@/core/utils'
import { fetchResumeExist } from '@/utils/helper/resume'
import Click from '@/components/Click'
import {
  store,
  useSelector,
} from '@/core/store'
import ClosedJobPage from './components/ClosedJobPage'
import MyExitDialog from './components/MyExitDialog'
import { haversineDistance } from '@/utils/location/index'
import GenerateResumePublish from './components/GenerateResumePublish'
import Header from '@/components/Header'
import { useLogin } from '@/core/utils/login'
import LoginBtn from '@/components/LoginBtn'

const headTagIds = [15, 17, 6, 16]
export default function Detail() {
  const [jobDetail, $jobDetail] = useState<JobDetail>({} as JobDetail)
  const [companyInfo, $companyInfo] = useState<any>({})
  const [loading, $loading] = useState<boolean>(true)
  const [isClosed, $isClosed] = useState(false)
  const { userId } = useSelector((state) => state.storage.userInfo)
  const isLogin = useSelector((state) => !!state.storage.token)
  const [showBack, $showBack] = useState(false)

  const { bottom } = useMemo(() => $.taro.getMenuButtonBoundingClientRect(), [])

  const headerHeight = bottom + 5

  const [publishShow, $publishShow] = useState(false)

  const cityId = $.getObjVal(jobDetail, 'urbanAreas.cityId', '')

  /** 是我的职位信息 */
  const isMyJob = useMemo(() => {
    return jobDetail.userId && jobDetail.userId == userId
  }, [jobDetail, userId])

  const refreshJobDetail = () => {
    // eslint-disable-next-line consistent-return
    return queryJobDetail(true)($.router.query || {})
      .then(([data, res]) => {
        if (res.code == 0 && data) {
          const endCode = $.getObjVal(data, 'isEnd.code', 1)
          if (endCode == 2) {
            $isClosed(true)
          }
          $jobDetail(data)
          return data
        }
      })
      .catch((error) => {
        $isClosed(true)
      })
  }

  useMemo(() => {
    const query = $.router.query || {}
    if (query.jobId) {
      queryJobDetail(false)(query)
        .then(async ([data, res]) => {
          if (res.code == 0 && data) {
            const endCode = $.getObjVal(data, 'isEnd.code', 1)
            if (endCode == 2) {
              $isClosed(true)
              $loading(false)
              return
            }
            $jobDetail(data)
            handleExposurePage(data)
            getCompanyInfo(data.userId)
              .then($companyInfo)
              .catch(() => $companyInfo({}))
              .finally(() => $.taro.nextTick(() => $loading(false)))
          } else if (res.code == 30010002) {
            $loading(false)
            $isClosed(true)
          } else {
            $.msg(res.message)
          }
        })
        .catch((error) => {
          $loading(false)
          $isClosed(true)
          console.error(error)
        })
    }
  }, [])

  const applied = $.getObjVal(jobDetail, 'aboutInfoSpecifics.isIm')

  const { headTags, resTags } = useMemo(() => {
    const headTags: any[] = []
    const resTags: any[] = []
    const { occIds = [] } = $.router.query
    const NumericIds = occIds.map(Number)
    const occShowTags = $.getObjVal(jobDetail, 'occShowTags', [] as any)

    let newOccShowTags = occShowTags.filter((item) => NumericIds.includes(item.occId))
    if (!newOccShowTags.length) {
      newOccShowTags = occShowTags
    }
    if (newOccShowTags.length) {
      newOccShowTags.forEach((item) => resTags.push({ name: item.occName, id: item.occId }))
      const showTags = newOccShowTags[0] ? newOccShowTags[0].showTags : []
      showTags
        .filter((item) => item.type !== 1)
        .forEach((item) => {
          if (headTagIds.includes(item.type)) {
            headTags.push(item)
          } else if (!newOccShowTags.some((tag) => tag.occName == item.name)) {
            resTags.push(item)
          }
        })
    }
    return {
      headTags: headTags.sort(
        (a, b) => headTagIds.indexOf(a.type) - headTagIds.indexOf(b.type),
      ),
      resTags,
    }
  }, [jobDetail])

  // eslint-disable-next-line sonarjs/cognitive-complexity, camelcase
  const apply = async (click_entry = '8', jobInfo = jobDetail) => {
    if (!store.getState().storage.token) {
      await $.login()
      jobInfo = await refreshJobDetail()
    }

    const applied = $.getObjVal(jobInfo, 'aboutInfoSpecifics.isIm')
    if (applied) {
      $.hideLoading()
      return
    }

    const existData = await fetchResumeExist(true)
    if (existData && !existData.exist) {
      $.hideLoading()
      /** 展示引导在线简历弹窗 */
      $publishShow(true)
      return
    }

    try {
      $.showLoading('投递中...')
      const buriedData = $.getObjVal($.router, 'params.buriedData', {} as any)
      const { jobId, occIds = [] } = $.router.query
      const occV2 = occIds.length ? [{ industry: -1, occIds }] : []
      await applyPreCheck({
        jobId,
        scene: 19,
        occV2,
        algorithmid: buriedData.backend_id,
      }).then(() => {
        uploadStatisticsData('clickBoss', jobInfo, {
          // eslint-disable-next-line camelcase
          click_entry,
          get_status: 1,
        })
        // 上报投递成功
        $.router.event({ jobId: jobInfo.jobId }, false)
        return refreshJobDetail()
      }).then(() => {
        $.hideLoading()
      })
    } catch ([_, error]) {
      uploadStatisticsData('clickBoss', jobInfo, {
        // eslint-disable-next-line camelcase
        click_entry,
        get_status: 0,
      })
      $.hideLoading()
      if (error instanceof Error) {
        $.msg('网络异常')
      } else if (error.code !== 0) {
        const dialogData = $.getObjVal(error, 'dialogData')
          || $.getObjVal(error, 'data.dialogData')
        if (
          dialogData
          && dialogData.dialogIdentify === 'statusRestrictPublish'
        ) {
          $.confirm({
            title: '温馨提示',
            content:
              '您的账号状态异常，部分功能被限制使用。如有疑问，请联系客服处理。电话：************',
            cancelText: '确定',
            confirmText: '联系客服',
          }).then(() => $.taro.makePhoneCall({ phoneNumber: '4008381888' }))
        } else if (dialogData && dialogData.dialogIdentify == 'chat_limit') {
          $.alert({
            title: '温馨提示',
            content: '您今日聊得太多，休息一下明天再来吧~',
            confirmText: '确定',
          })
          /** 当前信息已下架 */
        } else if (error && error.code == 30010001) {
          // $.msg(error.message)
          $isClosed(true)
        } else {
          $.confirm({
            title: '温馨提示',
            content: '投递失败,如有疑问,请联系客服处理。电话:************',
            confirmText: '联系客服',
            cancelText: '确定',
          }).then(() => $.taro.makePhoneCall({ phoneNumber: '4008381888' }))
        }
      }
    }
  }

  /** 期望工作地默认只取详情第一个工种 */
  const hopeOcc = [] as any
  if (jobDetail.occV2 && jobDetail.occV2.length) {
    hopeOcc.push({
      industry: jobDetail.occV2[0].industry || -1,
      occIds: jobDetail.occV2[0]?.occIds?.slice(0, 1) || [],

    })
  }

  const [loginProps] = useLogin({ success: async () => {
    const jobInfo = await refreshJobDetail()
    apply(undefined, jobInfo)
  },
  disableSuccessMsg: true })

  return (
    <Page>
      <View
        className={styles.container}
        style={{ paddingTop: `${headerHeight}px` }}
      >
        <Header classNames={styles.header} style={{ height: `${headerHeight}px` }} />
        <Skeleton
          loading={loading}
          style={{ top: `${headerHeight}px` }}
        ></Skeleton>
        <ClosedJobPage
          visible={isClosed}
          isEmployment={false}
        ></ClosedJobPage>
        <View
          className={classNames(styles.columns, { [styles.hidden]: isClosed })}
        >
          <HeaderCard info={jobDetail} headTags={headTags}></HeaderCard>
          <EmployerCard
            info={jobDetail}
            companyInfo={companyInfo}
            applied={applied}
            certApply={apply}
          ></EmployerCard>
          <ContentCard
            info={jobDetail}
            resTags={resTags}
            loading={loading}
          ></ContentCard>
          <CompanyAddressMap
            info={jobDetail}
            companyInfo={companyInfo}
          ></CompanyAddressMap>
          <YpSecurityCard></YpSecurityCard>
          <View
            className={classNames(styles.safety, {
              [styles.safetyNoFooter]: isMyJob,
            })}
          ></View>
          <View
            className={classNames(styles.footerBar, {
              [styles.hidden]: isMyJob,
            })}
          >{
              isLogin ? <Click
                className={classNames(styles.footerBtn, {
                  [styles.applied]: applied,
                })}
                onClick={() => apply()}
              >
                {!applied ? (
                  <Image
                    src="https://cdn.yupaowang.com/other/fabu-icon-img.png"
                    className={styles.fabuIcon}
                  />
                ) : (
                  ''
                )}
                {applied ? '已报名' : '免费报名'}
              </Click> : <LoginBtn {...loginProps} className={classNames(styles.footerBtn, {
                [styles.applied]: applied,
              })}
              >
                <Image
                  src="https://cdn.yupaowang.com/other/fabu-icon-img.png"
                  className={classNames(styles.fabuIcon, { [styles.hidden]: applied })}
                />
                { applied ? '已报名' : '免费报名' }</LoginBtn>
            }

          </View>
        </View>
      </View>
      <MyExitDialog
        visible={showBack}
        onClose={() => $showBack(false)}
      ></MyExitDialog>
      <GenerateResumePublish sourceId='3' visible={publishShow} hopeAreas={cityId ? [cityId] : []} occupations={hopeOcc} onClose={() => $publishShow(false)} onConfirm={apply} />
    </Page>
  )
}

const getCompanyInfo = (userId) => {
  if (!userId || userId == 0) {
    return Promise.reject()
  }
  return $.request['POST/enterprise/v1/enterpriseHomepage/getEnterpriseCard'](
    {
      userId,
      tenantKey: 'YPZP',
    },
    { hideMsg: true },
  ).then(([data, res]) => {
    if (res.code == 0 && data) {
      const financeScaleContent = getLabelByValue(
        FINANCE_SCALE_MAP,
        data.fundingScale,
      )
      const staffScaleContent = getLabelByValue(
        STAFF_SIZE_MAP,
        data.staffScale,
      )
      return {
        ...data,
        companyScaleText: [financeScaleContent, staffScaleContent]
          .filter((str) => !!str)
          .join(' · '),
      }
    }
    return Promise.reject()
  })
}

const applyPreCheck = (params: any) => {
  return $.request['POST/job/v3/contact/job/im/preCheck'](delNullOp(params), {
    hideMsg: true,
  }).then(([_, res]) => {
    return applyJob(params)
  })
}

const applyJob = (params) => {
  return $.request['POST/job/v3/contact/job/im/call'](delNullOp(params), {
    hideMsg: true,
  }).then(([data]) => {
    return data
  })
}
const handleExposurePage = async (info) => {
  const { latitude: myLat, longitude: myLon } = store.getState().storage.userLocation
  const { buriedData = {} } = $.router.data
  const distance = haversineDistance(myLat, myLon, info.location?.latitude, info.location?.longitude)
  // eslint-disable-next-line camelcase
  const { divisionline_area, type_options, job_options, ...rest } = buriedData
  $.report.event('enterRecruitmentDetails', {
    info_id: String(info.jobId),
    keywords_source: '',
    detailed_address: info.address,
    post_distance: distance,
    job_location: [info.location?.longitude, info.location?.latitude].filter(Boolean).join(','),
    search_result: '-99999',
    position_status: String(info.isEnd.code),
    free_information: null,
    consumption_product_score: null,
    fix_price_id: '0',
    real_name_view: '-99999',
    check_degree: String(info.checkDegreeStatus),
    feedback_exposure: '-99999',
    occupations_type: info.occMode == 2 ? '招聘' : '订单',
    ...rest,

  })
}
